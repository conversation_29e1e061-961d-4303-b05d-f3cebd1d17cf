"use client"

import {useState, useTransition} from "react"
import {But<PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger} from "@/components/ui/tabs"
import {Save} from "lucide-react"

import {ApplicationType} from "@/lib/types";
import {updateApplication} from "@/features/application/application.action";
import {useRouter} from "next/navigation";
import toast from "react-hot-toast";
import {applicationSettingsSchema} from "@/lib/zod-schemas";
import FormErrorMessage from "@/components/common/form-message-error";

// رابط دامنه
interface Domain extends ApplicationType {
    id: string
    name?: string
    description?: string
    logoUrl?: string
    faviconUrl?: string
    primaryColor?: string
    isActive?: boolean
    isDefault?: boolean
    smsTemplates?: {
        welcome: string
        verification: string
        resetPassword: string
        serviceConfirmation: string
        paymentConfirmation: string
    }
}

// رابط تنظیمات برنامه
interface AppSettings {
    domains: ApplicationType[]
}

type Props = {
    applications: ApplicationType[]
}

export default function AdminSettingsComponent({applications}: Props) {

    // تنظیمات پیش‌فرض با دامنه‌ها
    const [settings, setSettings] = useState<AppSettings>({
        domains: applications,
    })
    const [errors, setErrors] = useState<Partial<Record<keyof Domain, any>>>({})
    const [isPending, startTransition] = useTransition()
    const router = useRouter()

    // کپی از تنظیمات اصلی برای امکان بازگشت به حالت قبل
    // const [originalSettings, setOriginalSettings] = useState<AppSettings>({...settings})

    // دامنه انتخاب شده فعلی
    const [selectedDomainId, setSelectedDomainId] = useState<string | undefined>()

    // دامنه انتخاب شده فعلی
    const selectedDomain = settings?.domains.find((domain) => domain.applicationId === selectedDomainId)

    // ذخیره تنظیمات
    const handleSaveSettings = () => {
        if (isPending) return
        startTransition(async () => {
            if (selectedDomain) {
                const result = applicationSettingsSchema.safeParse(selectedDomain);
                if (!result.success) {
                    const fieldErrors: typeof errors = {};
                    result.error.errors.forEach(err => {
                        const field = err.path[0] as keyof Domain;
                        fieldErrors[field] = err.message;
                    });
                    toast.error(Object.values(fieldErrors)[0]);
                    setErrors(fieldErrors);
                } else {
                    setErrors({});
                    const actionResult = await updateApplication({
                        id: selectedDomain.applicationId,
                        title: selectedDomain.title,
                        pattern_login: selectedDomain.pattern_login,
                        merchent_id: selectedDomain.merchent_id,
                        ga_apiSecret: selectedDomain.ga_apiSecret,
                        ga_containerId: selectedDomain.ga_containerId,
                        ga_measurementId: selectedDomain.ga_measurementId
                    })

                    if (actionResult.success) {
                        router.refresh()
                    }

                    if (actionResult.success) {
                        router.refresh()
                    }
                }
            }
        })

    }

    // بازگشت به تنظیمات قبلی
    const handleResetSettings = () => {
        // setSettings({...originalSettings})
    }

    // بروزرسانی تنظیمات عمومی
    const updateGeneralSettings = () => {
        // setSettings({
        //     ...settings,
        //     general: {
        //         ...settings.general,
        //         [key]: value,
        //     },
        // })
    }

    // بروزرسانی تنظیمات پیامک
    const updateSmsSettings = () => {
        // setSettings({
        //     ...settings,
        //     sms: {
        //         ...settings.sms,
        //         [key]: value,
        //     },
        // })
    }

    // بروزرسانی تنظیمات اعلان
    const updateNotificationSettings = () => {
        // setSettings({
        //     ...settings,
        //     notification: {
        //         ...settings.notification,
        //         [key]: value,
        //     },
        // })
    }

    // بروزرسانی تنظیمات امنیتی
    const updateSecuritySettings = () => {
        // setSettings({
        //     ...settings,
        //     security: {
        //         ...settings.security,
        //         [key]: value,
        //     },
        // })
    }

    // بروزرسانی اطلاعات دامنه
    const updateDomainInfo = (key: keyof Domain, value: string) => {
        setSettings({
            ...settings,
            domains: settings.domains.map((domain) =>
                domain.applicationId === selectedDomainId ? {...domain, [key]: value} : domain,
            ),
        })
    }

     const updateMerchentIdInfo = (key: keyof Domain, value: string) => {
        setSettings({
            ...settings,
            domains: settings.domains.map((domain) =>
                domain.applicationId === selectedDomainId ? {...domain, [key]: value} : domain,
            ),
        })
    }

    

    // بروزرسانی الگوهای پیامک دامنه
    const updateDomainSmsTemplate = (key: keyof Domain["smsTemplates"], value: string) => {
        // setSettings({
        //     ...settings,
        //     domains: settings.domains.map((domain) =>
        //         domain.id === selectedDomainId
        //             ? {
        //                 ...domain,
        //                 smsTemplates: {
        //                     ...domain.smsTemplates,
        //                     [key]: value,
        //                 },
        //             }
        //             : domain,
        //     ),
        // })
    }

    // تنظیم دامنه پیش‌فرض
    const setDefaultDomain = (domainId: string) => {
        // setSettings({
        //     ...settings,
        //     domains: settings.domains.map((domain) => ({
        //         ...domain,
        //         isDefault: domain.id === domainId,
        //     })),
        // })
    }

    // حذف دامنه
    const deleteDomain = (domainId: string) => {
        // اگر دامنه پیش‌فرض است، نمی‌توان آن را حذف کرد
        // const domainToDelete = settings.domains.find((d) => d.id === domainId)
        // if (domainToDelete?.isDefault) {
        //     return
        // }
        //
        // // حذف دامنه
        // const updatedDomains = settings.domains.filter((domain) => domain.id !== domainId)
        // setSettings({
        //     ...settings,
        //     domains: updatedDomains,
        // })
        //
        // // اگر دامنه انتخاب شده حذف شد، دامنه دیگری را انتخاب کنید
        // if (domainId === selectedDomainId && updatedDomains.length > 0) {
        //     setSelectedDomainId(updatedDomains[0].id)
        // }

    }

    // پیش‌نمایش الگوی پیامک
    const previewSmsTemplate = (template: string, placeholders: Record<string, string>) => {
        let result = template
        for (const [key, value] of Object.entries(placeholders)) {
            result = result.replace(`{${key}}`, value)
        }
        return result
    }

    return (
        <div className="h-full min-h-[700px] flex-1 flex-col space-y-8 p-8 md:flex">
            <div className="flex items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">تنظیمات برنامه</h2>
                    <p className="text-muted-foreground mt-2">تنظیمات عمومی، دامنه‌ها، پیامک‌ها، اعلان‌ها و امنیت را
                        مدیریت
                        کنید.</p>
                </div>
                <div className="flex items-center gap-2">
                    {/*<Button variant="outline" onClick={handleResetSettings}>*/}
                    {/*    <RotateCcw className="ml-2 h-4 w-4"/>*/}
                    {/*    بازنشانی*/}
                    {/*</Button>*/}
                    <Button disabled={isPending || !selectedDomain}
                            className='bg-blue-600 cursor-pointer hover:bg-blue-500'
                            onClick={handleSaveSettings}>
                        <Save className="ml-2 h-4 w-4"/>
                        ذخیره تنظیمات
                    </Button>
                </div>
            </div>

            <Tabs defaultValue="domains" className="space-y-4">
                {/*<TabsList className="grid grid-cols-5 md:w-[750px]">*/}
                {/*    <TabsTrigger value="domains">*/}
                {/*        <Globe className="ml-2 h-4 w-4"/>*/}
                {/*        دامنه‌ها*/}
                {/*    </TabsTrigger>*/}
                {/*    <TabsTrigger value="general">*/}
                {/*        <Globe className="ml-2 h-4 w-4"/>*/}
                {/*        عمومی*/}
                {/*    </TabsTrigger>*/}
                {/*    <TabsTrigger value="sms">*/}
                {/*        <MessageSquare className="ml-2 h-4 w-4"/>*/}
                {/*        پیامک*/}
                {/*    </TabsTrigger>*/}
                {/*    <TabsTrigger value="notification">*/}
                {/*        <Bell className="ml-2 h-4 w-4"/>*/}
                {/*        اعلان‌ها*/}
                {/*    </TabsTrigger>*/}
                {/*    <TabsTrigger value="security">*/}
                {/*        <Shield className="ml-2 h-4 w-4"/>*/}
                {/*        امنیت*/}
                {/*    </TabsTrigger>*/}
                {/*</TabsList>*/}

                {/* تنظیمات دامنه‌ها */}
                <TabsContent value="domains" className='min-h-[400px]'>
                    <div className="!h-full grid gap-6 grid-cols-1 lg:grid-cols-4">
                        {/* لیست دامنه‌ها */}
                        <Card className="lg:col-span-1 !h-full">
                            <CardHeader>
                                <CardTitle>دامنه‌ها</CardTitle>
                                <CardDescription>دامنه‌های موجود در سیستم</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    {settings.domains.map((domain) => (
                                        <div
                                            key={domain.applicationId}
                                            className={`flex items-center justify-between p-3 rounded-md cursor-pointer ${
                                                selectedDomainId === domain.applicationId ? "!bg-blue-500/10 border !border-blue-500/30" : "hover:bg-blue-500/10"
                                            }`}
                                            onClick={() => setSelectedDomainId(domain.applicationId)}
                                        >
                                            <div className="flex items-center gap-2">
                                                {/*<div className="w-3 h-3 rounded-full"*/}
                                                {/*></div>*/}
                                                <span className="text-sm text-right">{domain.source}</span>
                                                {/*<div className="flex gap-1">*/}
                                                {/*    {domain.isDefault && (*/}
                                                {/*        <Badge variant="outline" className="text-xs">*/}
                                                {/*            پیش‌فرض*/}
                                                {/*        </Badge>*/}
                                                {/*    )}*/}
                                                {/*    {domain.isActive ? (*/}
                                                {/*        <Badge variant="success"*/}
                                                {/*               className="bg-green-500 text-white text-xs">*/}
                                                {/*            فعال*/}
                                                {/*        </Badge>*/}
                                                {/*    ) : (*/}
                                                {/*        <Badge variant="destructive" className="bg-gray-400 text-xs">*/}
                                                {/*            غیرفعال*/}
                                                {/*        </Badge>*/}
                                                {/*    )}*/}
                                                {/*</div>*/}
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {/* افزودن دامنه جدید */}
                                {/*<div className="pt-4">*/}
                                {/*    <h3 className="text-sm font-medium mb-2">افزودن دامنه جدید</h3>*/}
                                {/*    <div className="space-y-2">*/}
                                {/*        <Input*/}
                                {/*            placeholder="نام دامنه (مثال: example.com)"*/}
                                {/*            value={newDomain.name}*/}
                                {/*            onChange={(e) => setNewDomain({...newDomain, name: e.target.value})}*/}
                                {/*        />*/}
                                {/*        <Input*/}
                                {/*            placeholder="عنوان دامنه"*/}
                                {/*            value={newDomain.title}*/}
                                {/*            onChange={(e) => setNewDomain({...newDomain, title: e.target.value})}*/}
                                {/*        />*/}
                                {/*        <Button className="w-full" onClick={addNewDomain}*/}
                                {/*                disabled={!newDomain.name || !newDomain.title}>*/}
                                {/*            <PlusCircle className="ml-2 h-4 w-4"/>*/}
                                {/*            افزودن دامنه*/}
                                {/*        </Button>*/}
                                {/*    </div>*/}
                                {/*</div>*/}
                            </CardContent>
                        </Card>

                        {/* تنظیمات دامنه انتخاب شده */}
                        {selectedDomain && <Card className="lg:col-span-3">
                            <CardHeader className="flex flex-row items-center justify-between">
                                <div>
                                    <CardTitle>تنظیمات دامنه {selectedDomain?.title}</CardTitle>
                                    <CardDescription className='mt-2'>تنظیمات اختصاصی این دامنه را مدیریت
                                        کنید</CardDescription>
                                </div>
                                <div className="flex gap-2">
                                    {/*{!selectedDomain.isDefault && (*/}
                                    {/*    <Button variant="outline" onClick={() => setDefaultDomain(selectedDomain?.applicationId)}>*/}
                                    {/*        تنظیم به عنوان پیش‌فرض*/}
                                    {/*    </Button>*/}
                                    {/*)}*/}

                                    {/*<AlertDialog>*/}
                                    {/*    <AlertDialogTrigger asChild>*/}
                                    {/*        <Button variant="destructive">*/}
                                    {/*            <Trash2 className="ml-2 h-4 w-4"/>*/}
                                    {/*            حذف دامنه*/}
                                    {/*        </Button>*/}
                                    {/*    </AlertDialogTrigger>*/}
                                    {/*    <AlertDialogContent>*/}
                                    {/*        <AlertDialogHeader>*/}
                                    {/*            <AlertDialogTitle>آیا از حذف این دامنه اطمینان دارید؟</AlertDialogTitle>*/}
                                    {/*            <AlertDialogDescription>*/}
                                    {/*                این عمل قابل بازگشت نیست. دامنه {selectedDomain.title} و تمام*/}
                                    {/*                تنظیمات*/}
                                    {/*                آن حذف خواهد شد.*/}
                                    {/*            </AlertDialogDescription>*/}
                                    {/*        </AlertDialogHeader>*/}
                                    {/*        <AlertDialogFooter>*/}
                                    {/*            <AlertDialogCancel>انصراف</AlertDialogCancel>*/}
                                    {/*            <AlertDialogAction*/}
                                    {/*                onClick={() => deleteDomain(selectedDomain.applicationId)}*/}
                                    {/*                className="bg-destructive text-destructive-foreground"*/}
                                    {/*            >*/}
                                    {/*                حذف*/}
                                    {/*            </AlertDialogAction>*/}
                                    {/*        </AlertDialogFooter>*/}
                                    {/*    </AlertDialogContent>*/}
                                    {/*</AlertDialog>*/}
                                </div>
                            </CardHeader>

                            <CardContent className="space-y-6">
                                <Tabs defaultValue="domain-info">
                                    <TabsList className="w-full">
                                        <TabsTrigger value="domain-info" className="flex-1 cursor-pointer">
                                            اطلاعات دامنه
                                        </TabsTrigger>
                                        <TabsTrigger value="domain-analytics" className="flex-1 cursor-pointer">
                                            آنالیتیکس
                                        </TabsTrigger>
                                        <TabsTrigger value="domain-sms" className="flex-1 cursor-pointer">
                                            الگوهای پیامک
                                        </TabsTrigger>
                                        <TabsTrigger value="merchent-id" className="flex-1 cursor-pointer">
                                            مرچنت آیدی
                                        </TabsTrigger>
                                    </TabsList>

                                    {/* اطلاعات دامنه */}
                                    <TabsContent value="domain-info" className="space-y-6 pt-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="domain-name">نام دامنه</Label>
                                                <p className='pr-5'>{selectedDomain.source}</p>
                                                {/*<Input*/}
                                                {/*    id="domain-name"*/}
                                                {/*    value={selectedDomain.source}*/}
                                                {/*    onChange={(e) => updateDomainInfo("source", e.target.value)}*/}
                                                {/*/>*/}
                                            </div>
                                            <div className="space-y-2">
                                                <Label htmlFor="domain-title">عنوان دامنه</Label>
                                                <Input
                                                    id="domain-title"
                                                    value={selectedDomain.title}
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        updateDomainInfo("title", value)
                                                    }}
                                                />
                                                <FormErrorMessage message={errors?.title}/>
                                            </div>
                                        </div>
                                    </TabsContent>
                                    <TabsContent value="domain-analytics" className="space-y-6 pt-4">
                                        <div className="space-y-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="domain-container-id">Container ID (GTM)</Label>
                                                <Input
                                                    id="domain-container-id"
                                                    value={selectedDomain.ga_containerId || ""}
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        updateDomainInfo("ga_containerId", value)
                                                    }}
                                                    placeholder="GTM-XXXXXXX"
                                                    dir="ltr"
                                                />
                                                <FormErrorMessage message={errors?.ga_containerId}/>
                                                <p className="text-xs text-muted-foreground">شناسه کانتینر گوگل تگ منیجر
                                                    (مثال: GTM-XXXXXXX)</p>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="domain-api-secret">API Secret</Label>
                                                <Input
                                                    id="domain-api-secret"
                                                    value={selectedDomain.ga_apiSecret || ""}
                                                    placeholder="XXXXXXXXXXXXXXXXXXXXXXXX"
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        updateDomainInfo("ga_apiSecret", value)
                                                    }}
                                                    dir="ltr"
                                                />
                                                <FormErrorMessage message={errors?.ga_apiSecret}/>
                                                <p className="text-xs text-muted-foreground">
                                                    کلید مخفی API گوگل آنالیتیکس (مثال: XXXXXXXXXXXXXXXXXXXXXXXX)
                                                </p>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="domain-measurement-id">Measurement ID</Label>
                                                <Input
                                                    id="domain-measurement-id"
                                                    value={selectedDomain.ga_measurementId || ""}
                                                    placeholder="G-XXXXXXXXXX"
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        updateDomainInfo("ga_measurementId", value)
                                                    }}
                                                    dir="ltr"
                                                />
                                                <FormErrorMessage message={errors?.ga_measurementId}/>
                                                <p className="text-xs text-muted-foreground">
                                                    شناسه اندازه‌گیری گوگل آنالیتیکس 4 (مثال: G-XXXXXXXXXX)
                                                </p>
                                            </div>
                                        </div>
                                    </TabsContent>
                                    <TabsContent value="domain-sms" className="space-y-6 pt-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="pattern_login">کد الگو</Label>
                                                <Input
                                                    id="pattern_login"
                                                    inputMode="numeric"
                                                    pattern="[0-9]*"
                                                    value={selectedDomain.pattern_login}
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        if (/^\d*$/.test(value)) {
                                                            updateDomainInfo("pattern_login", e.target.value)
                                                        }
                                                    }}
                                                />
                                                <FormErrorMessage message={errors?.pattern_login}/>
                                            </div>
                                        </div>
                                    </TabsContent>
                                     <TabsContent value="merchent-id" className="space-y-6 pt-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="merchentId">مرچنت آیدی زرین پال</Label>
                                                <Input
                                                    id="merchentId"
                                                    inputMode="numeric"
                                                    pattern="[0-9]*"
                                                    value={selectedDomain.merchentId}
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        if (/^\d*$/.test(value)) {
                                                            updateMerchentIdInfo("merchent_id", e.target.value)
                                                        }
                                                    }}
                                                />
                                                <FormErrorMessage message={errors?.pattern_login}/>
                                            </div>
                                        </div>
                                    </TabsContent>
                                </Tabs>
                            </CardContent>
                        </Card>}
                    </div>
                </TabsContent>

                {/* تنظیمات عمومی */}
                {/*<TabsContent value="general">*/}
                {/*    <Card>*/}
                {/*        <CardHeader>*/}
                {/*            <CardTitle>تنظیمات عمومی</CardTitle>*/}
                {/*            <CardDescription>تنظیمات پایه برنامه را مدیریت کنید.</CardDescription>*/}
                {/*        </CardHeader>*/}
                {/*        <CardContent className="space-y-6">*/}
                {/*            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">*/}
                {/*                <div className="space-y-2">*/}
                {/*                    <Label htmlFor="default-language">زبان پیش‌فرض</Label>*/}
                {/*                    <Select*/}
                {/*                        value={settings.general.defaultLanguage}*/}
                {/*                        onValueChange={(value) => updateGeneralSettings("defaultLanguage", value)}*/}
                {/*                    >*/}
                {/*                        <SelectTrigger id="default-language">*/}
                {/*                            <SelectValue placeholder="انتخاب زبان"/>*/}
                {/*                        </SelectTrigger>*/}
                {/*                        <SelectContent>*/}
                {/*                            <SelectItem value="fa">فارسی</SelectItem>*/}
                {/*                            <SelectItem value="en">انگلیسی</SelectItem>*/}
                {/*                            <SelectItem value="ar">عربی</SelectItem>*/}
                {/*                        </SelectContent>*/}
                {/*                    </Select>*/}
                {/*                </div>*/}
                {/*                <div className="space-y-2">*/}
                {/*                    <Label htmlFor="contact-email">ایمیل تماس</Label>*/}
                {/*                    <Input*/}
                {/*                        id="contact-email"*/}
                {/*                        type="email"*/}
                {/*                        value={settings.general.contactEmail}*/}
                {/*                        onChange={(e) => updateGeneralSettings("contactEmail", e.target.value)}*/}
                {/*                    />*/}
                {/*                </div>*/}
                {/*                <div className="space-y-2">*/}
                {/*                    <Label htmlFor="contact-phone">شماره تماس</Label>*/}
                {/*                    <Input*/}
                {/*                        id="contact-phone"*/}
                {/*                        value={settings.general.contactPhone}*/}
                {/*                        onChange={(e) => updateGeneralSettings("contactPhone", e.target.value)}*/}
                {/*                    />*/}
                {/*                </div>*/}
                {/*            </div>*/}

                {/*            <div className="flex items-center space-x-2 pt-4">*/}
                {/*                <Switch*/}
                {/*                    id="dark-mode"*/}
                {/*                    checked={settings.general.enableDarkMode}*/}
                {/*                    onCheckedChange={(checked) => updateGeneralSettings("enableDarkMode", checked)}*/}
                {/*                />*/}
                {/*                <Label htmlFor="dark-mode" className="mr-2">*/}
                {/*                    فعال‌سازی حالت تاریک*/}
                {/*                </Label>*/}
                {/*            </div>*/}
                {/*        </CardContent>*/}
                {/*    </Card>*/}
                {/*</TabsContent>*/}

                {/* تنظیمات پیامک */}
                {/*<TabsContent value="sms">*/}
                {/*    <Card>*/}
                {/*        <CardHeader>*/}
                {/*            <CardTitle>تنظیمات پیامک</CardTitle>*/}
                {/*            <CardDescription>تنظیمات ارسال پیامک را مدیریت کنید.</CardDescription>*/}
                {/*        </CardHeader>*/}
                {/*        <CardContent className="space-y-6">*/}
                {/*            <div className="flex items-center space-x-2">*/}
                {/*                <Switch*/}
                {/*                    id="enable-sms"*/}
                {/*                    checked={settings.sms.enableSms}*/}
                {/*                    onCheckedChange={(checked) => updateSmsSettings("enableSms", checked)}*/}
                {/*                />*/}
                {/*                <Label htmlFor="enable-sms" className="mr-2">*/}
                {/*                    فعال‌سازی سیستم پیامک*/}
                {/*                </Label>*/}
                {/*            </div>*/}

                {/*            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">*/}
                {/*                <div className="space-y-2">*/}
                {/*                    <Label htmlFor="sms-api-key">کلید API پیامک</Label>*/}
                {/*                    <Input*/}
                {/*                        id="sms-api-key"*/}
                {/*                        value={settings.sms.apiKey}*/}
                {/*                        onChange={(e) => updateSmsSettings("apiKey", e.target.value)}*/}
                {/*                        disabled={!settings.sms.enableSms}*/}
                {/*                    />*/}
                {/*                </div>*/}
                {/*                <div className="space-y-2">*/}
                {/*                    <Label htmlFor="sms-sender">فرستنده پیامک</Label>*/}
                {/*                    <Input*/}
                {/*                        id="sms-sender"*/}
                {/*                        value={settings.sms.sender}*/}
                {/*                        onChange={(e) => updateSmsSettings("sender", e.target.value)}*/}
                {/*                        disabled={!settings.sms.enableSms}*/}
                {/*                    />*/}
                {/*                </div>*/}
                {/*            </div>*/}
                {/*        </CardContent>*/}
                {/*        <CardFooter>*/}
                {/*            <div className="flex items-center gap-2">*/}
                {/*                <Smartphone className="h-5 w-5 text-muted-foreground"/>*/}
                {/*                <p className="text-sm text-muted-foreground">*/}
                {/*                    پیامک‌ها از طریق سرویس‌دهنده پیامک تنظیم شده ارسال می‌شوند.*/}
                {/*                </p>*/}
                {/*            </div>*/}
                {/*        </CardFooter>*/}
                {/*    </Card>*/}
                {/*</TabsContent>*/}

                {/* تنظیمات اعلان‌ها */}
                {/*<TabsContent value="notification">*/}
                {/*    <Card>*/}
                {/*        <CardHeader>*/}
                {/*            <CardTitle>تنظیمات اعلان‌ها</CardTitle>*/}
                {/*            <CardDescription>تنظیمات اعلان‌های ایمیلی و پوش را مدیریت کنید.</CardDescription>*/}
                {/*        </CardHeader>*/}
                {/*        <CardContent className="space-y-6">*/}
                {/*            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">*/}
                {/*                <div className="space-y-4">*/}
                {/*                    <div className="flex items-center space-x-2">*/}
                {/*                        <Switch*/}
                {/*                            id="enable-email-notifications"*/}
                {/*                            checked={settings.notification.enableEmailNotifications}*/}
                {/*                            onCheckedChange={(checked) => updateNotificationSettings("enableEmailNotifications", checked)}*/}
                {/*                        />*/}
                {/*                        <Label htmlFor="enable-email-notifications" className="mr-2">*/}
                {/*                            فعال‌سازی اعلان‌های ایمیلی*/}
                {/*                        </Label>*/}
                {/*                    </div>*/}

                {/*                    <div className="flex items-center space-x-2">*/}
                {/*                        <Switch*/}
                {/*                            id="enable-push-notifications"*/}
                {/*                            checked={settings.notification.enablePushNotifications}*/}
                {/*                            onCheckedChange={(checked) => updateNotificationSettings("enablePushNotifications", checked)}*/}
                {/*                        />*/}
                {/*                        <Label htmlFor="enable-push-notifications" className="mr-2">*/}
                {/*                            فعال‌سازی اعلان‌های پوش*/}
                {/*                        </Label>*/}
                {/*                    </div>*/}
                {/*                </div>*/}

                {/*                <div className="space-y-2">*/}
                {/*                    <Label htmlFor="admin-email">ایمیل مدیر برای اعلان‌ها</Label>*/}
                {/*                    <Input*/}
                {/*                        id="admin-email"*/}
                {/*                        type="email"*/}
                {/*                        value={settings.notification.adminEmailForNotifications}*/}
                {/*                        onChange={(e) => updateNotificationSettings("adminEmailForNotifications", e.target.value)}*/}
                {/*                        disabled={!settings.notification.enableEmailNotifications}*/}
                {/*                    />*/}
                {/*                </div>*/}
                {/*            </div>*/}

                {/*            <div className="pt-4">*/}
                {/*                <h3 className="text-lg font-medium mb-4">تنظیمات رویدادها</h3>*/}
                {/*                <div className="space-y-4">*/}
                {/*                    <div className="flex items-center space-x-2">*/}
                {/*                        <Switch*/}
                {/*                            id="notify-new-service"*/}
                {/*                            checked={settings.notification.notifyOnNewService}*/}
                {/*                            onCheckedChange={(checked) => updateNotificationSettings("notifyOnNewService", checked)}*/}
                {/*                        />*/}
                {/*                        <Label htmlFor="notify-new-service" className="mr-2">*/}
                {/*                            اعلان برای خدمت جدید*/}
                {/*                        </Label>*/}
                {/*                    </div>*/}

                {/*                    <div className="flex items-center space-x-2">*/}
                {/*                        <Switch*/}
                {/*                            id="notify-new-user"*/}
                {/*                            checked={settings.notification.notifyOnNewUser}*/}
                {/*                            onCheckedChange={(checked) => updateNotificationSettings("notifyOnNewUser", checked)}*/}
                {/*                        />*/}
                {/*                        <Label htmlFor="notify-new-user" className="mr-2">*/}
                {/*                            اعلان برای کاربر جدید*/}
                {/*                        </Label>*/}
                {/*                    </div>*/}

                {/*                    <div className="flex items-center space-x-2">*/}
                {/*                        <Switch*/}
                {/*                            id="notify-payment"*/}
                {/*                            checked={settings.notification.notifyOnPayment}*/}
                {/*                            onCheckedChange={(checked) => updateNotificationSettings("notifyOnPayment", checked)}*/}
                {/*                        />*/}
                {/*                        <Label htmlFor="notify-payment" className="mr-2">*/}
                {/*                            اعلان برای پرداخت جدید*/}
                {/*                        </Label>*/}
                {/*                    </div>*/}
                {/*                </div>*/}
                {/*            </div>*/}
                {/*        </CardContent>*/}
                {/*    </Card>*/}
                {/*</TabsContent>*/}

                {/* تنظیمات امنیتی */}
                {/*<TabsContent value="security">*/}
                {/*    <Card>*/}
                {/*        <CardHeader>*/}
                {/*            <CardTitle>تنظیمات امنیتی</CardTitle>*/}
                {/*            <CardDescription>تنظیمات امنیتی و سیاست‌های دسترسی را مدیریت کنید.</CardDescription>*/}
                {/*        </CardHeader>*/}
                {/*        <CardContent className="space-y-6">*/}
                {/*            <div className="flex items-center space-x-2">*/}
                {/*                <Switch*/}
                {/*                    id="enable-two-factor"*/}
                {/*                    checked={settings.security.enableTwoFactor}*/}
                {/*                    onCheckedChange={(checked) => updateSecuritySettings("enableTwoFactor", checked)}*/}
                {/*                />*/}
                {/*                <Label htmlFor="enable-two-factor" className="mr-2">*/}
                {/*                    فعال‌سازی احراز هویت دو مرحله‌ای*/}
                {/*                </Label>*/}
                {/*            </div>*/}

                {/*            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">*/}
                {/*                <div className="space-y-2">*/}
                {/*                    <Label htmlFor="session-timeout">مدت زمان نشست (دقیقه)</Label>*/}
                {/*                    <Input*/}
                {/*                        id="session-timeout"*/}
                {/*                        type="number"*/}
                {/*                        min="5"*/}
                {/*                        max="1440"*/}
                {/*                        value={settings.security.sessionTimeout}*/}
                {/*                        onChange={(e) => updateSecuritySettings("sessionTimeout", Number.parseInt(e.target.value))}*/}
                {/*                    />*/}
                {/*                </div>*/}

                {/*                <div className="space-y-2">*/}
                {/*                    <Label htmlFor="max-login-attempts">حداکثر تلاش برای ورود</Label>*/}
                {/*                    <Input*/}
                {/*                        id="max-login-attempts"*/}
                {/*                        type="number"*/}
                {/*                        min="1"*/}
                {/*                        max="10"*/}
                {/*                        value={settings.security.maxLoginAttempts}*/}
                {/*                        onChange={(e) => updateSecuritySettings("maxLoginAttempts", Number.parseInt(e.target.value))}*/}
                {/*                    />*/}
                {/*                </div>*/}

                {/*                <div className="space-y-2">*/}
                {/*                    <Label htmlFor="password-policy">سیاست رمز عبور</Label>*/}
                {/*                    <Select*/}
                {/*                        value={settings.security.passwordPolicy}*/}
                {/*                        onValueChange={(value) => updateSecuritySettings("passwordPolicy", value)}*/}
                {/*                    >*/}
                {/*                        <SelectTrigger id="password-policy">*/}
                {/*                            <SelectValue placeholder="انتخاب سیاست"/>*/}
                {/*                        </SelectTrigger>*/}
                {/*                        <SelectContent>*/}
                {/*                            <SelectItem value="simple">ساده (حداقل ۶ کاراکتر)</SelectItem>*/}
                {/*                            <SelectItem value="medium">متوسط (حداقل ۸ کاراکتر با حروف و*/}
                {/*                                اعداد)</SelectItem>*/}
                {/*                            <SelectItem value="strong">*/}
                {/*                                قوی (حداقل ۸ کاراکتر با حروف بزرگ و کوچک، اعداد و کاراکترهای ویژه)*/}
                {/*                            </SelectItem>*/}
                {/*                        </SelectContent>*/}
                {/*                    </Select>*/}
                {/*                </div>*/}
                {/*            </div>*/}
                {/*        </CardContent>*/}
                {/*        <CardFooter>*/}
                {/*            <div className="flex items-center gap-2">*/}
                {/*                <Shield className="h-5 w-5 text-muted-foreground"/>*/}
                {/*                <p className="text-sm text-muted-foreground">*/}
                {/*                    تنظیمات امنیتی قوی به محافظت از داده‌های شما و کاربران کمک می‌کند.*/}
                {/*                </p>*/}
                {/*            </div>*/}
                {/*        </CardFooter>*/}
                {/*    </Card>*/}
                {/*</TabsContent>*/}
            </Tabs>
        </div>
    )
}
