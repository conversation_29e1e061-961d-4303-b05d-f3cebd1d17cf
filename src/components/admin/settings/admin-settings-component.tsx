"use client"

import {useState, useTransition} from "react"
import {But<PERSON>} from "@/components/ui/button"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger} from "@/components/ui/tabs"
import {Save} from "lucide-react"

import {ApplicationType} from "@/lib/types";
import {updateApplication} from "@/features/application/application.action";
import {useRouter} from "next/navigation";
import toast from "react-hot-toast";
import {applicationSettingsSchema} from "@/lib/zod-schemas";
import FormErrorMessage from "@/components/common/form-message-error";

// رابط دامنه
interface Domain extends ApplicationType {
    id: string
    name?: string
    description?: string
    logoUrl?: string
    faviconUrl?: string
    primaryColor?: string
    isActive?: boolean
    isDefault?: boolean
    smsTemplates?: {
        welcome: string
        verification: string
        resetPassword: string
        serviceConfirmation: string
        paymentConfirmation: string
    }
}

// رابط تنظیمات برنامه
interface AppSettings {
    domains: ApplicationType[]
}

type Props = {
    applications: ApplicationType[]
}

export default function AdminSettingsComponent({applications}: Props) {

    // تنظیمات پیش‌فرض با دامنه‌ها
    const [settings, setSettings] = useState<AppSettings>({
        domains: applications,
    })
    const [errors, setErrors] = useState<Partial<Record<keyof Domain, any>>>({})
    const [isPending, startTransition] = useTransition()
    const router = useRouter()

    // کپی از تنظیمات اصلی برای امکان بازگشت به حالت قبل
    // const [originalSettings, setOriginalSettings] = useState<AppSettings>({...settings})

    // دامنه انتخاب شده فعلی
    const [selectedDomainId, setSelectedDomainId] = useState<string | undefined>()

    // دامنه انتخاب شده فعلی
    const selectedDomain = settings?.domains.find((domain) => domain.applicationId === selectedDomainId)

    // ذخیره تنظیمات
    const handleSaveSettings = () => {
        if (isPending) return
        startTransition(async () => {
            if (selectedDomain) {
                const result = applicationSettingsSchema.safeParse(selectedDomain);
                if (!result.success) {
                    const fieldErrors: typeof errors = {};
                    result.error.errors.forEach(err => {
                        const field = err.path[0] as keyof Domain;
                        fieldErrors[field] = err.message;
                    });
                    toast.error(Object.values(fieldErrors)[0]);
                    setErrors(fieldErrors);
                } else {
                    setErrors({});
                    const updateData = {
                        id: selectedDomain.applicationId,
                        title: selectedDomain.title,
                        pattern_login: selectedDomain.pattern_login,
                        merchent_id: selectedDomain.merchent_id,
                        ga_apiSecret: selectedDomain.ga_apiSecret,
                        ga_containerId: selectedDomain.ga_containerId,
                        ga_measurementId: selectedDomain.ga_measurementId
                    };
                    console.log('Sending update data:', updateData);
                    const actionResult = await updateApplication(updateData)

                    if (actionResult.success) {
                        router.refresh()
                    }

                    if (actionResult.success) {
                        router.refresh()
                    }
                }
            }
        })

    }

   
   
    const updateDomainInfo = (key: keyof Domain, value: string) => {
        setSettings({
            ...settings,
            domains: settings.domains.map((domain) =>
                domain.applicationId === selectedDomainId ? {...domain, [key]: value} : domain,
            ),
        })
    }



   
    const previewSmsTemplate = (template: string, placeholders: Record<string, string>) => {
        let result = template
        for (const [key, value] of Object.entries(placeholders)) {
            result = result.replace(`{${key}}`, value)
        }
        return result
    }

    return (
        <div className="h-full min-h-[700px] flex-1 flex-col space-y-8 p-8 md:flex">
            <div className="flex items-center justify-between space-y-2">
                <div>
                    <h2 className="text-2xl font-bold tracking-tight">تنظیمات برنامه</h2>
                    <p className="text-muted-foreground mt-2">تنظیمات عمومی، دامنه‌ها، پیامک‌ها، اعلان‌ها و امنیت را
                        مدیریت
                        کنید.</p>
                </div>
                <div className="flex items-center gap-2">
                    {/*<Button variant="outline" onClick={handleResetSettings}>*/}
                    {/*    <RotateCcw className="ml-2 h-4 w-4"/>*/}
                    {/*    بازنشانی*/}
                    {/*</Button>*/}
                    <Button disabled={isPending || !selectedDomain}
                            className='bg-blue-600 cursor-pointer hover:bg-blue-500'
                            onClick={handleSaveSettings}>
                        <Save className="ml-2 h-4 w-4"/>
                        ذخیره تنظیمات
                    </Button>
                </div>
            </div>

            <Tabs defaultValue="domains" className="space-y-4">
                

                {/* تنظیمات دامنه‌ها */}
                <TabsContent value="domains" className='min-h-[400px]'>
                    <div className="!h-full grid gap-6 grid-cols-1 lg:grid-cols-4">
                        {/* لیست دامنه‌ها */}
                        <Card className="lg:col-span-1 !h-full">
                            <CardHeader>
                                <CardTitle>دامنه‌ها</CardTitle>
                                <CardDescription>دامنه‌های موجود در سیستم</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    {settings.domains.map((domain) => (
                                        <div
                                            key={domain.applicationId}
                                            className={`flex items-center justify-between p-3 rounded-md cursor-pointer ${
                                                selectedDomainId === domain.applicationId ? "!bg-blue-500/10 border !border-blue-500/30" : "hover:bg-blue-500/10"
                                            }`}
                                            onClick={() => setSelectedDomainId(domain.applicationId)}
                                        >
                                            <div className="flex items-center gap-2">
                                                {/*<div className="w-3 h-3 rounded-full"*/}
                                                {/*></div>*/}
                                                <span className="text-sm text-right">{domain.source}</span>
                                               
                                            </div>
                                        </div>
                                    ))}
                                </div>

                              
                            </CardContent>
                        </Card>

                        {/* تنظیمات دامنه انتخاب شده */}
                        {selectedDomain && <Card className="lg:col-span-3">
                            <CardHeader className="flex flex-row items-center justify-between">
                                <div>
                                    <CardTitle>تنظیمات دامنه {selectedDomain?.title}</CardTitle>
                                    <CardDescription className='mt-2'>تنظیمات اختصاصی این دامنه را مدیریت
                                        کنید</CardDescription>
                                </div>
                               
                            </CardHeader>

                            <CardContent className="space-y-6">
                                <Tabs defaultValue="domain-info">
                                    <TabsList className="w-full">
                                        <TabsTrigger value="domain-info" className="flex-1 cursor-pointer">
                                            اطلاعات دامنه
                                        </TabsTrigger>
                                        <TabsTrigger value="domain-analytics" className="flex-1 cursor-pointer">
                                            آنالیتیکس
                                        </TabsTrigger>
                                        <TabsTrigger value="domain-sms" className="flex-1 cursor-pointer">
                                            الگوهای پیامک
                                        </TabsTrigger>
                                        <TabsTrigger value="merchent-id" className="flex-1 cursor-pointer">
                                            مرچنت آیدی
                                        </TabsTrigger>
                                    </TabsList>

                                    {/* اطلاعات دامنه */}
                                    <TabsContent value="domain-info" className="space-y-6 pt-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="domain-name">نام دامنه</Label>
                                                <p className='pr-5'>{selectedDomain.source}</p>
                                                {/*<Input*/}
                                                {/*    id="domain-name"*/}
                                                {/*    value={selectedDomain.source}*/}
                                                {/*    onChange={(e) => updateDomainInfo("source", e.target.value)}*/}
                                                {/*/>*/}
                                            </div>
                                            <div className="space-y-2">
                                                <Label htmlFor="domain-title">عنوان دامنه</Label>
                                                <Input
                                                    id="domain-title"
                                                    value={selectedDomain.title}
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        updateDomainInfo("title", value)
                                                    }}
                                                />
                                                <FormErrorMessage message={errors?.title}/>
                                            </div>
                                        </div>
                                    </TabsContent>
                                    <TabsContent value="domain-analytics" className="space-y-6 pt-4">
                                        <div className="space-y-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="domain-container-id">Container ID (GTM)</Label>
                                                <Input
                                                    id="domain-container-id"
                                                    value={selectedDomain.ga_containerId || ""}
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        updateDomainInfo("ga_containerId", value)
                                                    }}
                                                    placeholder="GTM-XXXXXXX"
                                                    dir="ltr"
                                                />
                                                <FormErrorMessage message={errors?.ga_containerId}/>
                                                <p className="text-xs text-muted-foreground">شناسه کانتینر گوگل تگ منیجر
                                                    (مثال: GTM-XXXXXXX)</p>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="domain-api-secret">API Secret</Label>
                                                <Input
                                                    id="domain-api-secret"
                                                    value={selectedDomain.ga_apiSecret || ""}
                                                    placeholder="XXXXXXXXXXXXXXXXXXXXXXXX"
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        updateDomainInfo("ga_apiSecret", value)
                                                    }}
                                                    dir="ltr"
                                                />
                                                <FormErrorMessage message={errors?.ga_apiSecret}/>
                                                <p className="text-xs text-muted-foreground">
                                                    کلید مخفی API گوگل آنالیتیکس (مثال: XXXXXXXXXXXXXXXXXXXXXXXX)
                                                </p>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="domain-measurement-id">Measurement ID</Label>
                                                <Input
                                                    id="domain-measurement-id"
                                                    value={selectedDomain.ga_measurementId || ""}
                                                    placeholder="G-XXXXXXXXXX"
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        updateDomainInfo("ga_measurementId", value)
                                                    }}
                                                    dir="ltr"
                                                />
                                                <FormErrorMessage message={errors?.ga_measurementId}/>
                                                <p className="text-xs text-muted-foreground">
                                                    شناسه اندازه‌گیری گوگل آنالیتیکس 4 (مثال: G-XXXXXXXXXX)
                                                </p>
                                            </div>
                                        </div>
                                    </TabsContent>
                                    <TabsContent value="domain-sms" className="space-y-6 pt-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="pattern_login">کد الگو</Label>
                                                <Input
                                                    id="pattern_login"
                                                    inputMode="numeric"
                                                    pattern="[0-9]*"
                                                    value={selectedDomain.pattern_login}
                                                    onChange={(e) => {
                                                        const value = e.target.value;
                                                        if (/^\d*$/.test(value)) {
                                                            updateDomainInfo("pattern_login", e.target.value)
                                                        }
                                                    }}
                                                />
                                                <FormErrorMessage message={errors?.pattern_login}/>
                                            </div>
                                        </div>
                                    </TabsContent>
                                     <TabsContent value="merchent-id" className="space-y-6 pt-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-2">
                                                <Label htmlFor="merchentId">مرچنت آیدی زرین پال</Label>
                                                <Input
                                                    id="merchentId"
                                                    inputMode="text"
                                                    value={selectedDomain.merchent_id || ""}
                                                    onChange={(e) => { updateDomainInfo("merchent_id", e.target.value)}}
                                                />
                                                <FormErrorMessage message={errors?.merchent_id}/>
                                            </div>
                                        </div>
                                    </TabsContent>
                                </Tabs>
                            </CardContent>
                        </Card>}
                    </div>
                </TabsContent>

               
            </Tabs>
        </div>
    )
}
