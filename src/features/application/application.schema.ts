import {model, Model, models, Schema} from 'mongoose';
import {IApplication} from "@/lib/types";
import {SOURCE_NAME} from "@/lib/constants";

const ApplicationSchema = new Schema<IApplication>(
    {
        status: {
            type: Boolean,
            default: true
        },
        pattern_login: {
            type: String,
            required: true
        },
        source: {
            type: String,
            required: true,
            unique: true
        },
        title: {
            type: String,
            required: true
        },
        subset: {
            type: String,
            enum: [SOURCE_NAME],
            default: SOURCE_NAME,
        },
        ga_apiSecret: {
            type: String,
        },
        ga_measurementId: {
            type: String,
        },
        ga_containerId: {
            type: String,
        }
    },
    {
        timestamps: true,
        collection: 'applications',
    }
);

const ApplicationModel: Model<IApplication> =
    (models.applications) ||
    model<IApplication>("applications", ApplicationSchema);

export default ApplicationModel;

